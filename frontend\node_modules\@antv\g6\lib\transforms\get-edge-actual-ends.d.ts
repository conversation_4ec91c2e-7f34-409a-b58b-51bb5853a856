import { DataController } from '../runtime/data';
import type { EdgeData } from '../spec';
import { BaseTransform } from './base-transform';
import type { DrawData } from './types';
/**
 * <zh/> 获取边的实际端点
 *
 * <en/> Get the actual endpoints of the edge
 */
export declare class GetEdgeActualEnds extends BaseTransform {
    beforeDraw(input: DrawData): DrawData;
}
export declare const getEdgeEndsContext: (model: DataController, edge: EdgeData) => EdgeData;
