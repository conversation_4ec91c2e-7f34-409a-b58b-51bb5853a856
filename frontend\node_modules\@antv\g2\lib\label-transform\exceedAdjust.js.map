{"version": 3, "file": "exceedAdjust.js", "sourceRoot": "", "sources": ["../../src/label-transform/exceedAdjust.ts"], "names": [], "mappings": ";;;AAIA,0CAAsC;AAEtC,MAAM,cAAc,GAAG,CAAC,MAAc,EAAE,IAAY,EAAE,EAAE;IACtD,MAAM,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAC1D,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;IAE5C,IAAI,OAAO,GAAG,CAAC,EACb,OAAO,GAAG,CAAC,CAAC;IAEd,SAAS;IACT,IAAI,IAAI,GAAG,QAAQ,EAAE;QACnB,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;KAC3B;SAAM,IAAI,IAAI,GAAG,QAAQ,EAAE;QAC1B,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;KAC3B;IAED,SAAS;IACT,IAAI,IAAI,GAAG,QAAQ,EAAE;QACnB,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;KAC3B;SAAM,IAAI,IAAI,GAAG,QAAQ,EAAE;QAC1B,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC;KAC3B;IAED,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5B,CAAC,CAAC;AASF;;GAEG;AACI,MAAM,YAAY,GAA6B,CAAC,OAAO,GAAG,EAAE,EAAE,EAAE;IACrE,OAAO,CAAC,MAAuB,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE;QACrD,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QAE9D,iDAAiD;QACjD,MAAM,eAAe,GAAG,GAAG,EAAE;YAC3B,IAAI,MAAM,KAAK,MAAM,EAAE;gBACrB,yDAAyD;gBACzD,MAAM,EACJ,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,KAAK,GAAG,CAAC,EACT,MAAM,GAAG,CAAC,EACV,UAAU,GAAG,CAAC,EACd,WAAW,GAAG,CAAC,EACf,SAAS,GAAG,CAAC,EACb,YAAY,GAAG,CAAC,EAChB,WAAW,GAAG,CAAC,EACf,YAAY,GAAG,CAAC,EAChB,UAAU,GAAG,CAAC,EACd,aAAa,GAAG,CAAC,GAClB,GAAG,MAAM,CAAC;gBAEX,OAAO;oBACL;wBACE,CAAC,GAAG,UAAU,GAAG,WAAW,GAAG,OAAO;wBACtC,CAAC,GAAG,SAAS,GAAG,UAAU,GAAG,OAAO;qBACrC;oBACD;wBACE,CAAC,GAAG,KAAK,GAAG,WAAW,GAAG,YAAY,GAAG,OAAO;wBAChD,CAAC,GAAG,MAAM,GAAG,YAAY,GAAG,aAAa,GAAG,OAAO;qBACpD;iBACQ,CAAC;aACb;iBAAM;gBACL,0CAA0C;gBAC1C,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;gBACvD,OAAO;oBACL,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC;oBAC1B,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,EAAE,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;iBAClC,CAAC;aACb;QACH,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;QAEvC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACnB,IAAA,YAAI,EAAC,CAAC,CAAC,CAAC;YACR,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,EACtB,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;YACrB,MAAM,WAAW,GAAG,cAAc,CAChC;gBACE,CAAC,IAAI,EAAE,IAAI,CAAC;gBACZ,CAAC,IAAI,EAAE,IAAI,CAAC;aACb;YACD,iEAAiE;YACjE,YAAY,CACb,CAAC;YACF,iCAAiC;YACjC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,KAAK,CAAC,eAAe,EAAE;gBAChD,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;aACjD;YACD,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AArEW,QAAA,YAAY,gBAqEvB"}