# 小微制造企业（天线场景）BOM全流程落地需求说明书（SRS）

**版本：** V1.0  
**日期：** 2025-08-18  
**适用对象：** 企业负责人、技术/工艺/生产/PMC/采购/仓库/财务/售后、实施顾问、软件供应商

## 目录

1. [文档目的与范围](#1-文档目的与范围)
2. [背景与关键问题](#2-背景与关键问题)
3. [目标与量化KPI](#3-目标与量化kpi)
4. [术语与定义](#4-术语与定义)
5. [业务原则与总体架构](#5-业务原则与总体架构)
6. [角色、权限与RACI](#6-角色权限与raci)
7. [总体流程（端到端）](#7-总体流程端到端)
8. [功能需求（模块化）](#8-功能需求模块化)
9. [数据模型与关键字段](#9-数据模型与关键字段)
10. [接口与集成](#10-接口与集成)
11. [非功能性要求](#11-非功能性要求)
12. [实施路线与里程碑](#12-实施路线与里程碑)
13. [验收测试用例（UAT）](#13-验收测试用例uat)
14. [风险与对策](#14-风险与对策)
15. [交付物清单](#15-交付物清单)
16. [附录](#16-附录)

## 1. 文档目的与范围

### 1.1 目的
建立以"核心BOM（150%）+订单快速派生（100%）+按单合单采购+包装/MOQ取整+长度型切割优化+余料台账+服务BOM"的闭环能力，减少材料浪费、提升协同与成本透明度。

### 1.2 范围
- 业务流程设计与优化
- 功能与数据架构
- 报表与KPI体系
- 集成与非功能性要求
- 实施与验收标准

### 1.3 不在范围
- 高级APS排程
- 深度PLM协同
- 跨多工厂复杂计划（可二期扩展）

## 2. 背景与关键问题

### 2.1 现状（以天线为例）
维护"核心BOM"，接单后复制改成"客户BOM"，据此采购与生产。

### 2.2 关键痛点
1. **版本管理混乱**：版本泛滥、回馈不到核心BOM，知识沉淀差
2. **材料浪费严重**：包装/MOQ与线缆切割造成浪费；尾料无账，形成"看不见的库存"
3. **采购效率低下**：订单未合单、未切割优化，采购与用料离散低效
4. **售后服务困难**：售后无结构化设备档案，配件定位慢，保修判定混乱

## 3. 目标与量化KPI

### 3.1 订单派生与可追溯（上线6个月内）
- 100%订单BOM由核心BOM+规则派生
- 可追溯差异Delta
- ECN平均闭环≤3工作日
- 变更通知到达率100%

### 3.2 降废目标
- 包装/MOQ浪费率≤5%
- 长度型尾料再利用率≥40%
- 切割废料率下降≥30%
- 合单率≥60%（滚动窗口内，按金额/数量）

### 3.3 成本与效率
- 接单T+1出"订单BOM+采购建议+预计毛利"
- 订单毛利偏差（标准 vs 实际）≤±5%

### 3.4 售后服务
- 服务BOM覆盖≥80%在保设备
- 首修成功率≥85%
- 备件填充率≥95%

## 4. 术语与定义

| 术语 | 定义 |
|------|------|
| 核心BOM（150%） | 包含所有可选/互斥/参数位的"产品族"BOM |
| 订单BOM（100%） | 基于核心BOM与配置规则派生出的本订单唯一结构 |
| EBOM/MBOM/PBOM/SBOM/ServiceBOM | 设计/工艺/生产/销售/服务视角BOM |
| ECN/ECO | 工程更改通知/指令 |
| MRP-lite | 轻量用料展开与请购建议（含合单与包装/倍数取整） |
| 余料台账 | 可再用尾料的账册（长度/批次/有效期/成本/占用关联） |
| As-Built / As-Maintained | 出厂结构 / 维护后结构 |

## 5. 业务原则与总体架构

### 5.1 核心业务原则
1. **一处真相**：核心BOM与规则库统一发布；订单BOM只做派生与冻结，不复制整表维护
2. **订单冻结**：订单绑定派生时刻的核心BOM版本与规则版本；升级需审批
3. **先合后采**：在滚动时间窗内聚合需求，统一包装取整与切割优化，再分摊到订单
4. **余料有账**：长度型与保质期物料建立余料台账与优先占用
5. **全链路闭环**：从设计/工艺→采购/库存/生产→财务成本→售后→ECN反馈

### 5.2 总体架构组件
- 核心BOM库（150%）+ 规则引擎
- 订单配置与派生引擎（100%）
- MRP-lite（合单/取整/替代建议）
- 长度型切割优化+余料台账
- 成本引擎（标准/实际/浪费分项）
- 服务BOM+设备档案+工单
- 报表KPI与质量闭环（ECN）

## 6. 角色、权限与RACI

### 6.1 系统角色
- BOM管理员
- 研发工程师
- 工艺工程师
- PMC/计划
- 采购
- 仓库
- 生产
- 质检
- 财务
- 销售
- 售后
- IT/系统管理员
- 管理层

### 6.2 权限要点
- 仅BOM管理员可发布/冻结/作废核心BOM与规则；订单BOM升级需审批
- 价格/成本字段仅财务可见；他人脱敏
- 服务BOM可读多、可改少；设备档案仅售后/质量可写
- 全关键操作留痕（人/时/旧值/新值/原因）

### 6.3 RACI矩阵（节选）

| 活动 | 负责(R) | 批准(A) | 咨询(C) | 知情(I) |
|------|---------|---------|---------|----------|
| 核心BOM与规则 | 研发/工艺 | BOM管理员 | 生产/采购/财务 | 管理层 |
| MRP与合单 | PMC | 计划经理 | 采购/仓库 | 财务 |
| 切割/余料 | 生产/仓库 | 生产主管 | PMC | 财务 |
| 服务BOM | 售后 | 售后经理 | 技术/质量 | 仓库 |
| 成本核算 | 财务 | 财务经理 | PMC/采购 | 管理层 |

## 7. 总体流程（端到端）

### 7.1 主要业务流程
1. **建模**：核心BOM（150%）+规则库发布→版本化管理
2. **接单**：选择产品族与版本→录入客户配置→派生订单BOM（冻结）
3. **用料**：MRP-lite多层展开→滚动窗口合单→包装/MOQ取整→替代建议
4. **长料**：进行切割优化→生成切割/发料计划→尾料预估
5. **采购**：生成请购建议→转采购单→到货建账（批次/盘长/有效期）
6. **生产**：按切割计划领/发/回料→尾料入账→台账更新
7. **成本**：标准/实际成本快照→浪费分项（包装/MOQ/切割/过期）
8. **售后**：出厂建档（As-Built/二维码）→ServiceBOM关联→工单/备件预占→As-Maintained更新
9. **质量闭环**：工单故障码与更换统计→ECN评审→核心BOM/规则/ServiceBOM更新

## 8. 功能需求（模块化）

### A. 物料主数据

#### A.1 基础字段
- 编码、名称、规格、单位、类别、状态、自制/外购、替代组、质检要求、附件/图纸

#### A.2 采购属性
- 供应商、价格（币种/含未税/生效期）、MOQ、包装倍数、交期、保质期

#### A.3 工艺/降废属性
- IsLength(Y/N)、盘长、最小切割单位、切割预留、工序损耗率、余料可用(Y/N)、最小可用尾料长度

#### A.4 约束条件
- 编码唯一、禁用重复命名
- 禁硬删，使用禁用+重定向

### B. 核心BOM（150%）与规则库

#### B.1 核心BOM行
- 层级、子件、单位、QtyPer（可表达式）、可选/必选、互斥/依赖标签、替代组、备注（工艺/工位）

#### B.2 规则类型
- 包含/排除/替代（基于选项：Band/Connector/Color/IP等级…）
- 参数化用量（例：CableLen = L + CutAllowance；热缩管=函数(L)）
- 认证/合规选择（RoHS/REACH…）

#### B.3 版本控制
- 核心BOM版本与规则版本独立
- 差异对比与影响清单
- 发布/冻结/作废

### C. 订单配置与订单BOM派生（100%）

#### C.1 配置界面
- 选项选择+参数输入（范围/步进校验/默认值）

#### C.2 派生结果
- 订单BOM、差异Delta列表、所需新购/可用余料占用建议

#### C.3 订单冻结
- 绑定核心BOM与规则版本
- 支持审批后升级/回退

#### C.4 导出功能
- BOM、图纸/作业文件打包
- 打印工单/物料标签

### D. 变更管理（ECN/ECO）

#### D.1 变更流程
- 提出→影响分析（物料/BOM/订单/库存/成本/供方/服务BOM）→评审→批准→发布→生效→通知

#### D.2 生效策略
- 即时/定时/按订单
- 订单保留原版除非人工升级

#### D.3 通知机制
- 钉钉/企业微信/飞书+邮件
- 签收回执
- 全链路日志

### E. MRP-lite（合单+包装取整+替代）

#### E.1 输入参数
- 订单数量/交期、订单BOM版本、库存/在途/安全库存、损耗、提前期、最小批量/倍数

#### E.2 合单策略
- 滚动时间窗（如72小时）聚合
- 急单白名单不合

#### E.3 包装/MOQ取整
- 向上取整，记录"预计余量/浪费"
- 可模拟不同时间窗的浪费差异

#### E.4 输出结果
- 请购建议、备料清单、缺料清单、预计浪费归因（包装/MOQ）

### F. 长度型材料切割优化与余料台账

#### F.1 输入参数
- 聚合长度需求、盘长、最小切割单位、单件预留、工序损耗率、台账余料列表

#### F.2 切割策略
- **基础**：先用尾料（大段优先），不足再开整盘；大件优先切割
- **进阶**：可切换启发式/近似求解器以降废

#### F.3 输出结果
- 切割计划（批次/切割列表/盘使用率/预计尾料）
- 尾料入账阈值（≥X cm）与库位

#### F.4 余料台账
- 来源订单/批次、剩余长度、有效期、库位、成本、可预占、已占用订单

### G. 采购与到货

#### G.1 采购管理
- 请购转采购
- 供应商确认MOQ/交期偏差提醒与替代建议

#### G.2 到货管理
- 批次号、盘长/分盘、有效期、质检
- 差异回写成本与MRP

### H. 仓储/发料/回料

#### H.1 库存管理
- 按切割计划备/发料
- 替代料发料受控
- 退料/补料
- 批次/序列号追溯（关键件）

#### H.2 差异管理
- 理论vs实际耗用差异报表
- 报废流程与原因分类

### I. 成本与报价（含浪费分项）

#### I.1 标准成本
- BOM用量×标准价 + 包装/MOQ取整差额 + 切割预计浪费 + 工时/制造费

#### I.2 实际成本
- 按实际采购价/发料/尾料结转
- 浪费成本分项（包装/MOQ/切割/过期）

#### I.3 报价模拟
- 输入客户配置→即时材料成本与建议售价/毛利
- 毛利底线校验

### J. 销售BOM/配置器（可选轻量）

#### J.1 销售配置
- 面向销售的配置界面（选项、依赖/互斥校验）
- 一键生成订单配置

#### J.2 规则共享
- 与规则库共用口径
- 导出配置单与客户确认文件

### K. 服务BOM与售后闭环

#### K.1 服务BOM
- 备件清单（替代关系/互换组/易损标识/建议库存级别/预估工时/工具/价格策略/保修属性）

#### K.2 设备档案
- SN/二维码、客户/安装地址、出厂/安装日期、As-Built与As-Maintained结构、保修起止、SLA、最近服务记录

#### K.3 工单管理
- 来源（客户/扫码/IM）、故障码、诊断、建议备件（基于SBOM与FSC）、备件预占→发货/现场更换→旧件回收/RMA→As-Maintained更新→结算（保内策略）

#### K.4 备件库存
- Min-Max补货、超龄/临期预警、通用件共享、与余料台账可控联动（符合规格和质量时）

#### K.5 质量闭环
- 工单故障码与更换件Pareto→触发ECN→更新核心BOM/规则/ServiceBOM

### L. 包装/标签BOM

#### L.1 包装管理
- 出厂包装/内外箱/缓冲/标签/说明书/合格证/二维码等形成独立包装BOM
- 订单BOM联动生成

#### L.2 质量控制
- 校验：缺件/错件拦截
- 打印对接

### M. 报表与看板（预置）

#### M.1 降废报表
- 物料浪费率（包装/MOQ/切割/过期分项）
- 余料利用率、合单率、取整偏差排行榜

#### M.2 运营报表
- 缺料率、备料及时率、一次领料准确率、库存周转天数、呆滞库存

#### M.3 财务报表
- 订单毛利（标准/实际）、成本偏差、供应商价格波动

#### M.4 售后报表
- 首修成功率、MTTR、备件Fill Rate、Top故障码与备件消耗、重复返修率

#### M.5 数据质量报表
- 物料重复、BOM行缺字段、未关联工艺、价格缺失预警

### N. 通知与集成

#### N.1 通知系统
- ECN、缺料、合单窗口到期、余料到期、成本异常、工单与保修到期
- 推送至钉钉/企业微信/飞书与邮件

#### N.2 系统集成
- ERP/财务（价格/凭证）
- WMS（库存/批次/库位/有效期）
- MES（报工/实际耗用）
- CRM（客户与工单）
- IM（审批/消息）

#### N.3 数据交换
- 导入导出：Excel/CSV模板
- API鉴权（Token/OAuth2），字段校验与错误报告

## 9. 数据模型与关键字段

### 9.1 物料主数据（Item）
```
ItemCode, Name, Spec, UoM, Category, Status, Make/Buy, MOQ, PackMultiple, 
LeadTime, StdPrice, Currency, ShelfLifeDays, IsLength, ReelLength, MinCutUnit, 
CutAllowance, ProcessLossRate, ReusableScrap(Y/N), MinReusableLen, AltGroup, 
Attachments[]
```

### 9.2 核心BOM（CoreBOM）
```
# CoreBOMHeader
FamilyCode, Version, Status, EffectiveFrom/To, Notes

# CoreBOMLine
Parent, Child, QtyPer(expr), UoM, Level, IsOptional, OptionTags[], AltGroup, Notes
```

### 9.3 规则引擎（Rule）
```
RuleID, Family, Version, Condition(json), Action(Include/Exclude/Replace/Formula), Priority
```

### 9.4 订单配置（OrderConfig）
```
OrderNo, Customer, Family, Qty, CoreBOMVer, RuleVer, Options{Key:Value}, FreezeFlag
```

### 9.5 订单BOM（OrderBOM）
```
OrderNo, Line#, Parent, Child, Qty, Source(Core/Rule), LossRate, Notes, Frozen
```

### 9.6 MRP计划（MRPPlan）
```
BucketID, Item, GrossReq, OnHand, InTransit, NetReq, PackRoundedQty, ExpectedWaste, WindowID
```

### 9.7 切割计划（CutPlan）
```
PlanID, Item, ReelSpec, TotalDemandLen, CutList[Len×Qty], Remnants[Len], UsageRate
```

### 9.8 余料台账（ScrapLedger）
```
LedgerID, Item, Length, Location, ExpireDate, SourceOrder, Cost, ReservedFor
```

### 9.9 成本快照（CostSnapshot）
```
OrderNo, StdCost, ActCost, WasteCost{Pack, MOQ, Cut, Expiry}, Margin, Timestamp
```

### 9.10 服务BOM（ServiceBOM）
```
# ServiceBOMHeader
SBOMNo, Model/Family, Version, Effectivity, Attachments

# ServiceBOMLine
Item, ReplaceWith/InterchangeGroup, WearPart(Y/N), StockLevel(A/B/C), KitID, 
EstLabor(min), Tools, PricePolicy, WarrantyPolicy
```

### 9.11 设备档案（InstalledBase）
```
SN, OrderNo, Customer, Site, AsBuiltBOMRef, AsMaintainedBOMRef, WarrantyFrom/To, 
SLA, QRCode
```

### 9.12 服务工单（ServiceOrder）
```
SO#, Source, FSC, Diagnose, PartsSuggested[], PartsReserved[], Labor, Travel, 
WarrantyJudge, Settlement
```

## 10. 接口与集成

### 10.1 数据导入导出

#### 10.1.1 导入功能
- **主数据导入**：物料主数据、供应商信息、客户信息
- **BOM导入**：核心BOM、订单BOM、服务BOM
- **规则导入**：配置规则、替代规则、认证规则
- **价格导入**：标准价格、供应商报价、历史价格
- **库存导入**：当前库存、在途库存、安全库存
- **订单导入**：销售订单、采购订单、生产订单
- **字段校验**：数据类型、格式、范围、依赖关系校验
- **失败处理**：失败行回执、错误原因说明、修正建议

#### 10.1.2 导出功能
- **订单BOM导出**：标准模板、客户定制格式
- **采购建议导出**：请购单、供应商询价单
- **切割计划导出**：切割指令、物料标签
- **成本快照导出**：成本分析报表、毛利分析
- **服务工单导出**：工单详情、备件清单、结算单据
- **标准模板**：Excel/CSV格式，支持批量导出

### 10.2 API接口设计

#### 10.2.1 核心API端点
```
# 物料管理
GET/POST/PUT/DELETE /api/v1/items
GET /api/v1/items/{itemCode}/alternatives

# BOM管理
GET/POST/PUT /api/v1/boms
GET /api/v1/boms/{familyCode}/versions
POST /api/v1/boms/{familyCode}/derive

# 规则引擎
GET/POST/PUT /api/v1/rules
POST /api/v1/rules/validate

# 订单管理
GET/POST/PUT /api/v1/orders
POST /api/v1/orders/{orderNo}/configure
GET /api/v1/orders/{orderNo}/bom

# MRP计划
POST /api/v1/mrp/plan
GET /api/v1/mrp/requirements
POST /api/v1/mrp/consolidate

# 切割优化
POST /api/v1/cut/plan
GET /api/v1/cut/optimization

# 余料台账
GET/POST/PUT /api/v1/scrap-ledger
POST /api/v1/scrap-ledger/reserve

# 成本管理
GET /api/v1/cost/snapshot/{orderNo}
POST /api/v1/cost/calculate

# 服务BOM
GET/POST/PUT /api/v1/service/sbom
GET /api/v1/service/parts-suggestion

# 设备档案
GET/POST/PUT /api/v1/installed-base
GET /api/v1/installed-base/{sn}/history

# 服务工单
GET/POST/PUT /api/v1/service/orders
POST /api/v1/service/orders/{soNo}/parts-reserve
```

#### 10.2.2 API规范
- **认证方式**：JWT Token / OAuth2.0
- **数据格式**：JSON（请求/响应）
- **错误处理**：标准HTTP状态码 + 错误详情
- **限流控制**：每分钟1000次调用限制
- **版本管理**：URL路径版本控制（/api/v1/）
- **文档标准**：OpenAPI 3.0规范

### 10.3 单点登录与消息集成

#### 10.3.1 单点登录（SSO）
- **钉钉集成**：支持钉钉扫码登录、组织架构同步
- **企业微信集成**：企业微信身份认证、部门权限映射
- **飞书集成**：飞书账号登录、审批流程集成
- **LDAP支持**：企业AD域账号集成
- **多因子认证**：短信验证码、邮箱验证

#### 10.3.2 消息推送与Webhook
- **ECN变更通知**：变更内容、影响范围、审批状态推送到相关群组
- **缺料预警**：库存不足、交期延误、替代建议推送
- **工单提醒**：新工单创建、处理超时、客户满意度反馈
- **成本异常**：毛利偏差、价格波动、浪费超标预警
- **Webhook配置**：自定义推送规则、消息模板、推送频率

### 10.4 内部系统集成

#### 10.4.1 ERP系统集成
- **物料主数据同步**：编码、名称、规格、单位
- **价格信息同步**：标准价格、供应商报价
- **财务凭证生成**：采购入库、生产领料、销售出库
- **库存数据实时同步**：库存数量、批次信息、库位

#### 10.4.2 WMS系统集成
- **库存实时查询**：可用库存、在途库存、预留库存
- **批次追溯**：批次号、生产日期、有效期、供应商
- **库位管理**：货位分配、拣货路径、盘点差异
- **出入库单据**：领料单、退料单、调拨单

#### 10.4.3 MES系统集成
- **生产报工**：工序完工、实际耗用、质量数据
- **设备状态**：设备运行状态、故障信息、维护记录
- **质量数据**：检验结果、不合格品处理、质量追溯

#### 10.4.4 CRM系统集成
- **客户信息同步**：客户档案、联系方式、信用等级
- **销售订单**：订单状态、交期承诺、客户需求
- **服务工单**：客户报修、服务记录、满意度评价

### 10.5 外部系统集成

#### 10.5.1 供应商平台
- **价格更新**：实时价格、批量折扣、有效期
- **交期确认**：订单确认、生产计划、发货通知
- **订单状态**：订单进度、质量检验、发货跟踪
- **VMI/寄售**：库存代管、自动补货、结算对账

#### 10.5.2 物流系统
- **发货跟踪**：物流单号、运输状态、预计到达
- **到货确认**：签收确认、货物状态、异常处理
- **运费计算**：运输方式、重量体积、运费标准

#### 10.5.3 质量系统
- **检验结果**：来料检验、过程检验、成品检验
- **不合格品处理**：不合格原因、处理方式、责任追溯
- **供应商评价**：质量评级、改进建议、合格供应商名录

## 11. 非功能性要求

### 11.1 易用性要求
- **操作简化**：关键操作≤3步完成，减少用户学习成本
- **快速上手**：新用户2小时内掌握基本操作
- **移动端支持**：移动端可查阅BOM、审批请购、处理工单
- **扫码功能**：扫码秒开设备档案，快速定位配件信息
- **界面友好**：直观的操作界面，清晰的导航结构
- **智能提示**：操作引导、错误提示、建议推荐
- **快捷操作**：常用功能快捷键、批量操作、模板复用

### 11.2 性能要求
- **BOM展开性能**：1万行BOM展开≤5秒
- **合单优化性能**：200单合单+取整+切割优化≤30秒
- **页面响应**：页面加载≤3秒，查询响应≤2秒
- **并发处理**：支持100个并发用户同时操作
- **批量处理**：支持10000条记录批量导入
- **实时计算**：成本计算、库存查询实时响应
- **报表生成**：复杂报表生成≤10秒

### 11.3 可用性与安全要求
- **系统可用性**：SaaS环境可用性≥99.9%（年度）
- **故障恢复**：RTO≤2小时，RPO≤30分钟
- **备份策略**：每日自动备份，异地容灾
- **最小权限原则**：用户仅能访问职责范围内的功能和数据
- **字段脱敏**：价格、成本等敏感信息按角色脱敏显示
- **全链路审计**：关键操作全程记录，支持审计追溯
- **数据加密**：传输加密（HTTPS），存储加密（AES-256）
- **访问控制**：基于角色的权限控制（RBAC）
- **会话管理**：自动登出、并发会话控制
- **安全认证**：多因子认证、密码策略

### 11.4 可维护性要求
- **配置化管理**：编码规则、业务规则、审批流程可配置
- **报表配置**：报表字段、格式、权限可灵活配置
- **无需二开覆盖**：通过配置满足≥80%的业务场景需求
- **参数调优**：合单窗口、切割参数、预警阈值可调整
- **版本管理**：系统版本控制、配置版本管理
- **日志管理**：详细的系统日志、业务日志、错误日志
- **监控告警**：系统性能监控、业务异常告警
- **文档完整**：操作手册、配置说明、故障排除指南

### 11.5 兼容性要求
- **浏览器支持**：Chrome 80+, Firefox 75+, Edge 80+, Safari 13+
- **移动端**：响应式设计，支持iOS/Android平板和手机
- **操作系统**：Windows 10+, Linux, macOS
- **数据格式**：支持Excel、CSV、XML、JSON格式导入导出
- **打印支持**：支持各种标签打印机、A4打印机

### 11.6 扩展性要求
- **模块化设计**：功能模块可独立部署和升级
- **数据库扩展**：支持分库分表，应对数据量增长
- **负载均衡**：支持水平扩展，应对用户量增长
- **API开放**：提供标准API接口，支持第三方集成
- **插件机制**：支持业务插件扩展，满足个性化需求

## 12. 实施路线与里程碑

### 12.1 总体实施策略（小步快跑）
- **快速迭代**：每个阶段1-4周，快速交付可用功能
- **价值优先**：优先实现高价值、高频使用的核心功能
- **试点验证**：每个阶段都有明确的验收标准和试点验证
- **持续改进**：基于用户反馈快速调整和优化

### 12.2 实施阶段规划

#### 阶段0：字典与编码（1周）
**目标**：建立标准化的数据基础

**交付物**：
- 选型确认（表格或轻量系统）
- 物料编码规则文档
- 配置字典（Band/Connector/CableLength/IP等）
- 主数据模板

**验收标准**：
- 编码规则覆盖主要物料类别
- 配置字典支持核心产品配置
- 主数据模板可直接使用

#### 阶段1：核心BOM+规则（2-3周）
**目标**：实现配置化BOM和订单派生

**交付物**：
- 1个产品族150%核心BOM+规则V1.0
- Delta对比功能
- BOM发布/冻结流程
- 订单一键派生功能

**验收标准**：
- 100%试点订单从配置派生订单BOM
- BOM变更可追溯
- 订单BOM冻结后不可随意修改

#### 阶段2：MRP-lite+合单+包装取整（2-3周）
**目标**：实现智能采购建议和浪费控制

**交付物**：
- 滚动窗口策略配置
- 请购建议算法
- 合单优化功能
- 包装取整计算
- 浪费归因报表

**验收标准**：
- 合单率≥50%
- 包装浪费可视化
- 采购建议准确率≥90%

#### 阶段3：切割优化+余料台账（3-4周）
**目标**：实现切割优化和余料利用

**交付物**：
- 切割计划算法
- 尾料入账与占用功能
- 余料台账管理
- 现场操作SOP
- 切割优化报表

**验收标准**：
- 尾料利用率≥30%
- 切割废料率下降≥20%
- 余料占用可追溯

#### 阶段4：服务BOM+设备档案+工单（2周）
**目标**：实现售后服务全流程管理

**交付物**：
- 出厂建档流程
- 设备二维码生成
- 服务BOM（SBOM）
- 工单管理+备件预占
- 保修策略配置

**验收标准**：
- 首次修复率（FTF）≥80%
- 备件满足率（Fill Rate）≥95%
- SBOM覆盖率≥80%

#### 阶段5：成本与看板（2周）
**目标**：实现成本核算和管理看板

**交付物**：
- 标准成本/实际成本计算
- 浪费分项统计
- 毛利看板
- Top故障码分析
- ECN闭环管理

**验收标准**：
- T+1出成本报表
- 毛利偏差≤±8%（后续迭代至±5%）
- 成本构成可追溯

### 12.3 关键里程碑

| 里程碑 | 时间节点 | 关键指标 | 验收标准 |
|--------|----------|----------|----------|
| M1 | 第1周末 | 数据标准化 | 编码规则、配置字典完成 |
| M2 | 第4周末 | 核心BOM | 试点产品100%订单派生 |
| M3 | 第7周末 | MRP优化 | 合单率≥50%，浪费可视化 |
| M4 | 第11周末 | 切割优化 | 尾料利用率≥30% |
| M5 | 第13周末 | 服务管理 | FTF≥80%，Fill Rate≥95% |
| M6 | 第15周末 | 成本看板 | T+1成本，毛利偏差≤±8% |

### 12.4 风险控制
- **每周进度检查**：及时发现和解决问题
- **阶段验收**：每个阶段必须通过验收才能进入下一阶段
- **回滚机制**：关键功能出现问题时可快速回滚
- **并行开发**：部分功能可并行开发，缩短总体周期

## 13. 验收测试用例（UAT）

### 13.1 核心业务流程测试

#### UC1：订单派生测试
**测试目标**：验证从核心BOM到订单BOM的完整派生流程
**前置条件**：已建立核心BOM(Family=AntennaX v1.0)
**测试步骤**：
1. 创建新订单，选择产品配置{Band=2.4G, Connector=SMA, L=1.5m}
2. 系统自动派生订单BOM
3. 检查Delta对比结果
4. 确认订单BOM并冻结
**预期结果**：
- 订单BOM正确生成，包含所有必需物料
- Delta对比显示配置差异
- 订单冻结后BOM不可随意修改

#### UC2：包装取整与合单测试
**测试目标**：验证包装优化和合单功能
**前置条件**：连接器需求=480只，包装规格=50只/包
**测试步骤**：
1. 运行MRP计算
2. 系统建议采购数量
3. 检查浪费归因
4. 执行合单优化
**预期结果**：
- 建议采购=500只（取整后）
- 预计浪费=20只，归因=包装取整
- 合单后与其他订单合并至550只，浪费=0

#### UC3：切割优化测试
**测试目标**：验证切割计划算法和尾料处理
**前置条件**：需求1.2m×100件与1.5m×80件，盘长=100m，最小切割=1cm
**测试步骤**：
1. 输入切割需求
2. 运行切割优化算法
3. 查看切割批次安排
4. 检查尾料入账规则
**预期结果**：
- 输出最优切割批次
- 盘使用率最大化
- ≥0.5m尾料自动入账，<0.5m计入切割浪费

#### UC4：余料优先占用测试
**测试目标**：验证余料台账管理和优先占用
**前置条件**：台账中存在1.0m尾料×50条
**测试步骤**：
1. 创建新订单需要1.0m线缆×80条
2. 系统自动占用台账余料
3. 检查占用记录
4. 验证不足部分的处理
**预期结果**：
- 自动占用台账中1.0m尾料×50条
- 不足30条从新盘开料
- 占用记录完整可追溯

### 13.2 变更管理测试

#### UC5：ECN生效测试
**测试目标**：验证工程变更通知的生效机制
**前置条件**：子件A需要替代为A'，设定生效日期
**测试步骤**：
1. 创建ECN，设定A→A'替代关系和生效日期
2. 在生效日前创建订单
3. 在生效日后创建订单
4. 检查通知推送
**预期结果**：
- 生效日前订单仍使用子件A
- 生效日后新订单自动使用A'
- 采购/仓库收到变更通知并签收

### 13.3 成本核算测试

#### UC6：成本快照测试
**测试目标**：验证标准成本和实际成本计算
**前置条件**：已有完整的BOM和价格数据
**测试步骤**：
1. 计算标准成本（含包装/切割预计浪费）
2. 生产完成后按实际发料计算
3. 结转尾料价值
4. 生成毛利分析
**预期结果**：
- 标准成本包含各项浪费预估
- 实际成本基于真实发料和尾料结转
- 毛利偏差≤±5%

### 13.4 服务管理测试

#### UC7：出厂建档与二维码测试
**测试目标**：验证设备出厂建档和二维码功能
**前置条件**：产品准备发货
**测试步骤**：
1. 发货时生成As-Built BOM
2. 创建Service BOM
3. 生成并打印设备二维码
4. 扫码验证设备档案
**预期结果**：
- As-Built BOM记录实际配置
- Service BOM包含可维修件
- 扫码可显示设备档案和可购配件

#### UC8：工单推荐测试
**测试目标**：验证故障诊断和备件推荐
**前置条件**：录入故障代码FSC-001
**测试步骤**：
1. 创建服务工单，录入故障代码
2. 系统推荐维修方案和备件
3. 一键预占备件并发货
4. 更新As-Maintained记录
**预期结果**：
- 系统推荐O形圈与密封胶
- 一键预占发货功能正常
- 保修判定准确
- As-Maintained记录更新

### 13.5 权限与安全测试

#### UC9：权限控制测试
**测试目标**：验证角色权限和数据安全
**前置条件**：不同角色用户登录
**测试步骤**：
1. 非管理员尝试发布核心BOM
2. 非财务人员查看价格字段
3. 执行关键操作
4. 检查审计日志
**预期结果**：
- 非管理员无法发布核心BOM和规则
- 价格字段对非财务角色隐藏
- 关键操作有完整日志记录

### 13.6 性能测试

#### UC10：性能基准测试
**测试目标**：验证系统性能指标
**测试数据**：1万行BOM，200个订单
**测试步骤**：
1. BOM展开性能测试
2. 合单+取整+切割优化测试
3. 并发用户访问测试
4. 大批量数据导入测试
**预期结果**：
- 1万行BOM展开≤5秒
- 200单合单+取整+切割优化≤30秒
- 支持100并发用户
- 批量导入响应正常

### 13.7 集成测试

#### UC11：API接口测试
**测试目标**：验证关键API接口功能
**测试接口**：/items, /boms, /rules, /orders, /mrp/plan等
**测试步骤**：
1. 测试数据导入导出API
2. 测试业务流程API
3. 验证数据格式和校验
4. 测试错误处理机制
**预期结果**：
- API响应正常，数据格式正确
- 字段校验有效，失败行有明确回执
- 错误处理机制完善

#### UC12：消息推送测试
**测试目标**：验证Webhook消息推送功能
**前置条件**：已配置钉钉/企业微信群
**测试步骤**：
1. 触发ECN变更
2. 产生缺料预警
3. 创建服务工单
4. 检查群消息推送
**预期结果**：
- ECN/缺料/工单消息及时推送到群
- 消息格式清晰，包含关键信息
- 推送失败有重试机制

## 14. 风险与对策

### 14.1 业务实施风险

#### 风险1：规则过度复杂
**风险描述**：配置规则过于复杂，导致系统难以维护和用户难以理解
**影响程度**：高
**应对措施**：
- 先覆盖Top80%配置与高金额物料
- 分阶段上线，从简单规则开始
- 建立规则库和最佳实践
- 提供规则配置向导和模板
- 定期评估和简化规则

#### 风险2：余料执行不到位
**风险描述**：现场执行不规范，余料回库和占用流程执行不到位
**影响程度**：中
**应对措施**：
- 尾料回库与占用纳入绩效考核
- 库位标识与扫码SOP标准化
- 现场培训和监督检查
- 建立余料利用率奖励机制
- 系统强制校验和提醒

#### 风险3：合单影响交期
**风险描述**：合单优化可能延长采购周期，影响紧急订单交期
**影响程度**：中
**应对措施**：
- 设置急单白名单，绕过合单流程
- 窗口参数可调，支持实时模拟
- 建立交期预警机制
- 提供手动干预功能
- 定期评估合单策略效果

#### 风险4：主数据质量问题
**风险描述**：主数据不准确或不完整，影响系统计算结果
**影响程度**：高
**应对措施**：
- 导入前数据清洗和校验
- 建立编码规则与重复校验机制
- 禁止硬删除，保留数据历史
- 建立数据质量看板和例会
- 设置数据责任人和审核流程

#### 风险5：工具选择失配
**风险描述**：选择的系统工具与业务需求不匹配，导致实施失败
**影响程度**：高
**应对措施**：
- 先跑通流程+模板PoC验证
- 要求供应商逐条响应本SRS
- 建立详细的功能对比矩阵
- 进行试点验证和用户反馈
- 保留系统切换的退出机制

### 14.2 技术实施风险

#### 风险6：系统性能不达标
**风险描述**：大数据量处理时系统响应缓慢，影响用户体验
**影响程度**：高
**应对措施**：
- 建立性能基准测试
- 数据库优化和索引设计
- 实施分布式架构
- 建立缓存机制
- 定期性能监控和调优

#### 风险7：数据安全风险
**风险描述**：敏感数据泄露或系统被攻击
**影响程度**：高
**应对措施**：
- 实施最小权限原则
- 敏感字段脱敏显示
- 建立全链路审计日志
- 定期安全评估和渗透测试
- 建立应急响应机制

#### 风险8：集成兼容性问题
**风险描述**：与现有ERP、WMS等系统集成出现兼容性问题
**影响程度**：中
**应对措施**：
- 提前进行集成测试
- 制定标准API接口规范
- 准备数据格式转换工具
- 建立集成监控和告警
- 与供应商建立技术支持通道

### 14.3 项目管理风险

#### 风险9：项目延期
**风险描述**：项目实施周期超出预期，影响业务计划
**影响程度**：中
**应对措施**：
- 采用小步快跑的敏捷实施方法
- 建立每周进度检查机制
- 关键路径管理和资源优化
- 预留缓冲时间和应急方案
- 建立项目风险预警机制

#### 风险10：用户接受度低
**风险描述**：用户对新系统接受度不高，影响推广效果
**影响程度**：中
**应对措施**：
- 充分的用户培训和操作指导
- 分阶段推广，从试点开始
- 建立用户反馈收集机制
- 设置系统使用激励措施
- 提供7×24小时技术支持

### 14.4 运维保障风险

#### 风险11：系统可用性不足
**风险描述**：系统故障频发，影响业务连续性
**影响程度**：高
**应对措施**：
- 建立系统监控告警机制
- 制定应急响应和故障恢复预案
- 实施每日自动备份和异地容灾
- 建立运维团队和值班制度
- 定期系统健康检查和维护

#### 风险12：知识传承风险
**风险描述**：关键人员离职导致系统维护困难
**影响程度**：中
**应对措施**：
- 建立完整的系统文档和操作手册
- 实施知识管理和经验分享
- 培养多名系统管理员
- 建立供应商技术支持服务
- 定期进行系统培训和演练

### 14.5 风险监控机制

#### 风险预警指标
- **数据质量**：主数据完整性≥95%，重复率≤2%
- **系统性能**：响应时间≤3秒，可用性≥99.9%
- **用户满意度**：培训通过率≥90%，用户满意度≥80%
- **业务效果**：合单率≥50%，余料利用率≥30%
- **项目进度**：里程碑达成率≥90%，延期风险≤10%

#### 风险应对流程
1. **风险识别**：定期风险评估和预警监控
2. **风险评估**：评估风险影响程度和发生概率
3. **风险应对**：制定和执行风险应对措施
4. **风险跟踪**：持续监控风险状态和应对效果
5. **经验总结**：形成风险管理知识库和最佳实践

## 15. 交付物清单

### 15.1 文档交付物

#### 业务规范文档
- **编码规则文档**：物料编码、产品编码、配置编码规范
- **核心BOM/规则SOP**：BOM建模、规则配置、版本管理标准操作程序
- **MRP与合单SOP**：需求计划、合单优化、采购建议标准流程
- **切割与余料SOP**：切割计划、余料管理、现场操作标准
- **服务BOM与工单SOP**：服务BOM建立、工单处理、备件管理流程
- **数据字典**：系统字段定义、代码含义、业务术语解释

#### 测试验收文档
- **UAT用例文档**：详细的用户验收测试用例和执行记录
- **KPI口径定义**：关键绩效指标的计算方法和统计口径
- **看板定义文档**：管理看板的指标定义、展示规则、权限设置

#### 技术文档
- **系统架构设计文档**：技术架构、部署架构、安全架构
- **接口规范文档**：API接口定义、数据格式、调用规范
- **数据库设计文档**：表结构、字段定义、索引设计
- **部署运维文档**：安装部署、配置管理、监控运维

### 15.2 模板交付物

#### 数据模板
- **物料主数据模板**：包含所有必需字段和校验规则
- **核心BOM模板（150%）**：覆盖主要产品族的完整BOM结构
- **订单BOM模板**：标准订单BOM格式和字段定义
- **采购建议模板**：MRP输出的采购建议标准格式
- **切割计划模板**：切割优化结果的标准输出格式
- **余料台账模板**：余料入库、占用、盘点的标准模板
- **成本快照模板**：标准成本、实际成本的分析模板
- **Service BOM模板**：售后服务BOM的标准格式
- **设备档案模板**：设备基础信息、配置信息、维修历史
- **工单模板**：服务工单的标准格式和处理流程

#### 报表模板
- **浪费分析报表**：包装浪费、切割浪费、过期浪费分析
- **成本分析报表**：标准成本、实际成本、差异分析
- **库存分析报表**：库存周转、呆滞分析、安全库存
- **服务分析报表**：故障统计、备件消耗、服务效率

### 15.3 系统配置交付物

#### 权限配置
- **角色权限矩阵**：各角色的功能权限和数据权限配置
- **用户组设置**：按部门、职能划分的用户组配置
- **数据脱敏规则**：敏感字段的脱敏显示规则
- **审计日志配置**：关键操作的日志记录规则

#### 业务配置
- **审批流配置**：ECN审批、请购审批、工单审批流程
- **通知订阅配置**：消息推送规则、订阅用户、推送渠道
- **合单窗口配置**：时间窗口、合单规则、优先级设置
- **切割与尾料参数**：切割精度、尾料阈值、利用策略
- **预警阈值配置**：库存预警、交期预警、成本预警设置

#### 集成配置
- **API接口配置**：第三方系统接口参数和认证配置
- **数据同步配置**：ERP、WMS、MES系统的数据同步规则
- **消息推送配置**：钉钉、企业微信、飞书的Webhook配置
- **单点登录配置**：SSO集成参数和用户映射规则

### 15.4 数据交付物

#### 历史数据
- **历史物料清洗包**：清洗后的历史物料主数据
- **历史BOM导入包**：转换后的历史BOM数据
- **版本映射表**：新旧系统的数据版本对应关系
- **基础配置数据**：字典数据、编码规则、初始参数

#### 测试数据
- **功能测试数据集**：各功能模块的测试数据
- **性能测试数据集**：大数据量的性能测试数据
- **集成测试数据集**：系统集成测试的模拟数据

### 15.5 培训交付物

#### 培训材料
- **管理员培训手册**：系统配置、用户管理、故障处理
- **最终用户操作手册**：按角色分类的操作指南
- **业务流程培训材料**：新流程的培训PPT和案例
- **视频教程库**：关键操作的录屏教程
- **快速参考卡**：常用功能的快速操作指南

#### 培训服务
- **现场培训服务**：为期3天的现场集中培训
- **在线培训平台**：支持远程学习和考试的在线平台
- **专家答疑服务**：实施期间的专家在线答疑
- **操作认证服务**：关键岗位的操作能力认证

### 15.6 运维支持交付物

#### 运维文档
- **系统运维手册**：日常运维、监控告警、故障处理
- **备份恢复方案**：数据备份策略、灾难恢复预案
- **性能调优指南**：系统性能监控和优化方法
- **安全管理规范**：用户管理、权限控制、安全审计

#### 支持服务
- **7×24技术支持**：实施期间的全天候技术支持
- **远程运维服务**：系统监控、故障诊断、远程修复
- **定期健康检查**：月度系统健康检查和优化建议
- **版本升级服务**：系统版本升级和功能增强服务

### 15.7 质量保证交付物

#### 测试报告
- **功能测试报告**：各功能模块的测试结果和缺陷统计
- **性能测试报告**：系统性能基准测试和压力测试结果
- **安全测试报告**：安全漏洞扫描和渗透测试结果
- **集成测试报告**：与第三方系统的集成测试结果

#### 验收文档
- **阶段验收报告**：各实施阶段的验收结果和签字确认
- **最终验收报告**：项目整体验收结果和用户满意度调查
- **问题跟踪清单**：发现问题的跟踪处理记录
- **改进建议报告**：系统优化和功能增强建议

## 16. 附录

### 16.1 关键公式

#### A. 包装取整公式
```
PurchaseQty = ceil(NetReq / PackMultiple) × PackMultiple
```
**说明**：
- NetReq：净需求数量
- PackMultiple：包装倍数
- ceil()：向上取整函数
- PurchaseQty：建议采购数量

#### B. 长度需求计算公式
```
LenReq = Σ[Qty × (CableLength + CutAllowance)] × (1 + ProcessLossRate)
```
**说明**：
- Qty：需求数量
- CableLength：线缆长度
- CutAllowance：切割余量
- ProcessLossRate：工艺损耗率
- LenReq：总长度需求

#### C. 尾料入账阈值判断
```
IF RemainLen ≥ MinReusableLen THEN 入台账 ELSE 计切割浪费
```
**说明**：
- RemainLen：剩余长度
- MinReusableLen：最小可重用长度

#### D. 浪费率计算公式（物料维度）
```
浪费率 = (包装浪费 + MOQ浪费 + 切割浪费 + 过期报废) / 期内采购量 × 100%
```

#### E. 成本计算公式
```
标准成本 = 物料成本 + 包装浪费成本 + 切割浪费成本 + 工艺损耗成本
实际成本 = 实际发料成本 - 尾料结转价值 + 实际浪费成本
```

### 16.2 样例规则（天线产品）

#### A. 连接器选择规则
```
IF Connector='SMA' THEN include CONN_SMA_50ohm 
ELSE include CONN_N_50ohm
```

#### B. 频段匹配规则
```
IF Band in {868,915} THEN PCB=PCB_SUBG
IF Band='2.4G' THEN PCB=PCB_24G
IF Band='5G' THEN PCB=PCB_5G
```

#### C. 线缆长度计算规则
```
Cable(m) = ceil((L(mm)+20)/1000 × Qty × (1+ProcessLoss), MinCutUnit)
```
**说明**：
- L(mm)：订单要求长度（毫米）
- +20：切割余量（毫米）
- ProcessLoss：工艺损耗率
- MinCutUnit：最小切割单位

#### D. 防护等级规则
```
IF IP>=67 THEN include SEALANT_X, O_RING_Y
IF IP>=65 THEN include GASKET_Z
```

#### E. 外观颜色规则
```
IF Color='Black' THEN Housing=HOUSING_BLACK 
ELSE Housing=HOUSING_WHITE
```

#### F. 包装规则
```
IF Qty<=10 THEN Package=SINGLE_BOX
IF Qty>10 AND Qty<=100 THEN Package=BULK_BOX
IF Qty>100 THEN Package=PALLET
```

### 16.3 小微企业特别关注清单

#### A. 包装/标签BOM标准化
- **说明书BOM**：多语言说明书、安装指南、保修卡
- **合格证BOM**：产品合格证、检验报告、认证证书
- **二维码标签**：设备序列号、配置信息、服务入口
- **包装材料**：防静电袋、缓冲材料、外包装箱
- **标识标签**：产品标签、警告标签、追溯标签

#### B. 合规追溯要求
- **RoHS/REACH合规**：有害物质检测报告、供应商声明
- **FCC/CE认证**：射频认证、电磁兼容认证、安全认证
- **关键件批次管理**：芯片、连接器、线缆的批次追溯
- **序列号管理**：产品唯一标识、生产追溯、质量追溯
- **供应商资质**：ISO认证、质量协议、审核报告

#### C. 供应商管理策略
- **第二来源策略**：关键物料的备选供应商
- **VMI/寄售模式**：高价值物料的供应商管理库存
- **停产件管理**：LTA（最后订购）协议、替代预案
- **供应商评估**：质量、交期、成本、服务综合评价
- **风险管控**：供应商财务风险、产能风险评估

#### D. 服务价目表与毛利核算
- **服务定价策略**：人工费、差旅费、备件费分项定价
- **保修策略**：保修期限、保修范围、免费/收费界定
- **备件定价**：成本加成、市场定价、批量折扣
- **服务毛利核算**：服务收入、服务成本、毛利分析
- **客户分级服务**：VIP客户、普通客户差异化服务

#### E. 现场SOP标准化
- **尾料回库SOP**：尾料标识、入库流程、库位管理
- **替代料使用SOP**：替代审批、使用记录、影响评估
- **保修判定SOP**：故障诊断、保修范围、收费标准
- **旧件回收SOP**：旧件分类、回收流程、价值评估
- **现场安全SOP**：安全防护、应急处理、事故报告

#### F. 客户自助服务
- **扫码获取手册**：产品手册、安装指南、故障排除
- **配件自助查询**：兼容配件、价格查询、在线订购
- **报修入口**：故障报修、进度查询、满意度评价
- **技术支持**：在线客服、技术论坛、视频教程
- **客户门户**：订单查询、发货跟踪、发票下载

#### G. 数据质量看板与例会
- **主数据质量**：完整性、准确性、一致性指标
- **变更管理质量**：ECN及时性、准确性、影响分析
- **成本数据质量**：成本及时性、准确性、差异分析
- **质量数据统计**：合格率、返工率、客户投诉率
- **数据质量例会**：周例会、月度总结、改进计划

### 16.4 术语表

| 术语 | 英文 | 定义 |
|------|------|------|
| 物料清单 | BOM | Bill of Materials，产品的组成结构 |
| 核心BOM | Core BOM | 包含所有可能配置的完整BOM模板 |
| 订单BOM | Order BOM | 根据客户配置从核心BOM派生的具体BOM |
| 服务BOM | Service BOM | 用于售后服务的可维修件清单 |
| 物料需求计划 | MRP | Material Requirements Planning |
| 工程变更通知 | ECN | Engineering Change Notice |
| 首次修复率 | FTF | First Time Fix，首次上门修复成功率 |
| 备件满足率 | Fill Rate | 备件库存满足服务需求的比率 |
| 最后订购 | LTA | Last Time Buy，停产件的最后采购 |
| 供应商管理库存 | VMI | Vendor Managed Inventory |

### 16.5 数据字典

#### 物料类型代码
- **RAW**：原材料（Raw Material）
- **SEMI**：半成品（Semi-finished）
- **FINI**：成品（Finished Product）
- **PACK**：包装材料（Packaging）
- **TOOL**：工具（Tool）
- **SPARE**：备件（Spare Part）

#### 单位代码
- **PCS**：个（Pieces）
- **KG**：千克（Kilogram）
- **M**：米（Meter）
- **L**：升（Liter）
- **SET**：套（Set）
- **ROLL**：卷（Roll）

#### BOM类型代码
- **CORE**：核心BOM
- **ORDER**：订单BOM
- **SERVICE**：服务BOM
- **PACKAGE**：包装BOM
- **OPTION**：可选件BOM

### 16.6 API接口规范

#### 核心业务API
```
GET /api/v1/items - 获取物料列表
GET /api/v1/items/{id} - 获取物料详情
POST /api/v1/boms - 创建BOM
GET /api/v1/boms/{id} - 获取BOM详情
POST /api/v1/rules - 创建配置规则
POST /api/v1/orders - 创建订单
POST /api/v1/mrp/plan - 执行MRP计算
POST /api/v1/cut/plan - 生成切割计划
GET /api/v1/scrap-ledger - 获取余料台账
GET /api/v1/cost/snapshot - 获取成本快照
POST /api/v1/service/sbom - 创建服务BOM
GET /api/v1/installed-base - 获取设备档案
POST /api/v1/service/orders - 创建服务工单
```

#### 数据格式示例
```json
{
  "itemCode": "M-CONN-SMA-001",
  "itemName": "SMA连接器50欧姆",
  "category": "连接器",
  "unit": "PCS",
  "packMultiple": 50,
  "minOrderQty": 100,
  "standardCost": 12.50,
  "supplier": "SUP001",
  "leadTime": 7
}
```

---

**文档状态：** 草案  
**最后更新：** 2025-08-18  
**版本控制：** V1.0  
**审核状态：** 待审核  

**联系信息：**  
项目经理：[姓名]  
技术负责人：[姓名]  
业务负责人：[姓名]  

**变更记录：**

| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| V1.0 | 2025-08-18 | 初始版本创建 | [姓名] |

---

*本文档为小微制造企业BOM全流程落地项目的核心需求文档，涵盖了从物料管理到售后服务的完整业务流程。文档将根据项目进展和业务需求持续更新完善。*