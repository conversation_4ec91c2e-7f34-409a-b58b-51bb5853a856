var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { lowerFirst, upperFirst, isPlainObject } from '@antv/util';
import { Band, Constant } from '@antv/scale';
/**
 * @description Get element's ancestor view node.
 * @param elemenet G2 element.
 * @returns Element's ancestor view node.
 */
export function getViewFromElement(element) {
    var _a;
    let current = element;
    while (current) {
        if (((_a = current.attributes) === null || _a === void 0 ? void 0 : _a.class) === 'view')
            return current;
        current = current.parentNode;
    }
    return null;
}
/**
 * @description Check if the element is a heatmap.
 * @param element G2 element.
 * @returns True if the element is a heatmap, otherwise false.
 */
export function isHeatmap(element) {
    const { markType, nodeName } = element;
    return markType === 'heatmap' && nodeName === 'image';
}
/**
 * @description Get element's original data.
 * @param elemenet G2 element.
 * @param elemenet View data, if not provided, will get from element's ancestor view.
 * @returns The original data of the element.
 */
export function dataOf(element, viewData) {
    const view = viewData !== null && viewData !== void 0 ? viewData : getViewFromElement(element).__data__;
    const datum = element.__data__;
    const { markKey, index, seriesIndex, normalized = { x: 0 } } = datum;
    const { markState } = view;
    const selectedMark = Array.from(markState.keys()).find((mark) => mark.key === markKey);
    if (!selectedMark)
        return;
    if (seriesIndex) {
        return seriesIndex.map((i) => selectedMark.data[i]);
    }
    return isHeatmap(element)
        ? selectedMark.data[Math.round(selectedMark.data.length * normalized.x)]
        : selectedMark.data[index];
}
/**
 * @description Get element's series name.
 * @param elemenet G2 element.
 * @returns The series name of the element.
 */
export function seriesOf(elemenet) {
    const viewData = getViewFromElement(elemenet).__data__;
    const { scale } = viewData;
    return groupNameOf(scale, elemenet.__data__);
}
/**
 * Get group name with view's scale and element's datum.
 */
export function groupNameOf(scale, datum) {
    const { color: scaleColor, series: scaleSeries, facet = false } = scale;
    const { color, series } = datum;
    const invertAble = (scale) => {
        return (scale &&
            scale.invert &&
            !(scale instanceof Band) &&
            !(scale instanceof Constant));
    };
    // For non constant color channel.
    if (invertAble(scaleSeries)) {
        const cloned = scaleSeries.clone();
        return cloned.invert(series);
    }
    if (series &&
        scaleSeries instanceof Band &&
        scaleSeries.invert(series) !== color &&
        !facet) {
        return scaleSeries.invert(series);
    }
    if (invertAble(scaleColor)) {
        const name = scaleColor.invert(color);
        // For threshold scale.
        if (Array.isArray(name))
            return null;
        return name;
    }
    return null;
}
export function identity(x) {
    return x;
}
/**
 * Composes functions from left to right.
 */
export function compose(fns) {
    return fns.reduce((composed, fn) => (x, ...args) => fn(composed(x, ...args), ...args), identity);
}
/**
 * Composes single-argument async functions from left to right.
 */
export function composeAsync(fns) {
    return fns.reduce((composed, fn) => (x) => __awaiter(this, void 0, void 0, function* () {
        const value = yield composed(x);
        return fn(value);
    }), identity);
}
export function capitalizeFirst(str) {
    return str.replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}
export function error(message = '') {
    throw new Error(message);
}
export function copyAttributes(target, source) {
    const { attributes } = source;
    const exclude = new Set(['id', 'className']);
    for (const [key, value] of Object.entries(attributes)) {
        if (!exclude.has(key)) {
            target.attr(key, value);
        }
    }
}
export function defined(x) {
    return x !== undefined && x !== null && !Number.isNaN(x);
}
export function random(a, b) {
    return a + (b - a) * Math.random();
}
export function useMemo(compute) {
    const map = new Map();
    return (key) => {
        if (map.has(key))
            return map.get(key);
        const value = compute(key);
        map.set(key, value);
        return value;
    };
}
export function appendTransform(node, transform) {
    const { transform: preTransform } = node.style;
    const unset = (d) => d === 'none' || d === undefined;
    const prefix = unset(preTransform) ? '' : preTransform;
    node.style.transform = `${prefix} ${transform}`.trimStart();
}
export function subObject(obj, prefix) {
    return maybeSubObject(obj, prefix) || {};
}
export function maybeSubObject(obj, prefix) {
    const entries = Object.entries(obj || {})
        .filter(([key]) => key.startsWith(prefix))
        .map(([key, value]) => [lowerFirst(key.replace(prefix, '').trim()), value])
        .filter(([key]) => !!key);
    return entries.length === 0 ? null : Object.fromEntries(entries);
}
export function prefixObject(obj, prefix) {
    return Object.fromEntries(Object.entries(obj).map(([key, value]) => {
        return [`${prefix}${upperFirst(key)}`, value];
    }));
}
export function filterPrefixObject(obj, prefix) {
    return Object.fromEntries(Object.entries(obj).filter(([key]) => prefix.find((p) => key.startsWith(p))));
}
export function omitPrefixObject(obj, ...prefixes) {
    return Object.fromEntries(Object.entries(obj).filter(([key]) => prefixes.every((prefix) => !key.startsWith(prefix))));
}
export function maybePercentage(x, size) {
    if (x === undefined)
        return null;
    if (typeof x === 'number')
        return x;
    const px = +x.replace('%', '');
    return Number.isNaN(px) ? null : (px / 100) * size;
}
export function isStrictObject(d) {
    return (typeof d === 'object' &&
        !(d instanceof Date) &&
        d !== null &&
        !Array.isArray(d));
}
export function isUnset(value) {
    return value === null || value === false;
}
export function deepAssign(dist, src, maxLevel = 5, level = 0) {
    if (level >= maxLevel)
        return;
    for (const key of Object.keys(src)) {
        const value = src[key];
        if (!isPlainObject(value) || !isPlainObject(dist[key])) {
            dist[key] = value;
        }
        else {
            deepAssign(dist[key], value, maxLevel, level + 1);
        }
    }
    return dist;
}
//# sourceMappingURL=helper.js.map