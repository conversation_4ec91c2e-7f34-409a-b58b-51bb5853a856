/**
 * <zh/> 创建插件容器
 *
 * <en/> Create a plugin container
 * @param type - <zh/> 插件类型 | <en/> plugin type
 * @param cover - <zh/> 容器是否覆盖整个画布 | <en/> Whether the container covers the entire canvas
 * @param style - <zh/> 额外样式 | <en/> Additional style
 * @returns <zh/> 插件容器 | <en/> plugin container
 */
export declare function createPluginContainer(type: string, cover?: boolean, style?: Partial<CSSStyleDeclaration>): HTMLElement;
/**
 * <zh/> 创建 DOM 元素，如果存在则删除，再创建一个新的
 *
 * <en/> Create a DOM element, if exists, remove it and create a new one.
 * @param id - <zh/> id | <en/> id
 * @param tag - <zh/> 标签 | <en/> tag
 * @param style - <zh/> 样式 | <en/> style
 * @param innerHTML - <zh/> 内容 | <en/> innerHTML
 * @param container - <zh/> 容器 | <en/> container
 * @returns <zh/> 创建的 DOM 元素 | <en/> created DOM element
 */
export declare function insertDOM(id: string, tag?: string, style?: Partial<CSSStyleDeclaration>, innerHTML?: string, container?: HTMLElement): HTMLElement;
