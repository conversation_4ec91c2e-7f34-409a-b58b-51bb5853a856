import type { DataChange, DataChanges } from '../types';
/**
 * <zh/> 对数据操作进行约简
 *
 * <en/> Reduce data changes
 * @param changes - <zh/> 数据操作 | <en/> data changes
 * @returns <zh/> 约简后的数据操作 | <en/> reduced data changes
 */
export declare function reduceDataChanges(changes: DataChange[]): DataChange[];
/**
 * <zh/> 对数据操作进行分类
 *
 * <en/> Classify data changes
 * @param changes - <zh/> 数据操作 | <en/> data changes
 * @returns <zh/> 分类后的数据操作 | <en/> classified data changes
 */
export declare function groupByChangeType(changes: DataChange[]): DataChanges;
