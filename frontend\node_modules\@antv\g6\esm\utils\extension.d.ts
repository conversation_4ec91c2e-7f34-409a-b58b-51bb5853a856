import type { STDExtensionOption } from '../registry/extension/types';
import type { Graph } from '../runtime/graph';
import type { TransformOptions } from '../spec/transform';
/**
 * <zh/> 将模块配置项转换为标准模块格式
 *
 * <en/> Convert extension options to standard format
 * @param graph - <zh/> 图实例 <en/> graph instance
 * @param category - <zh/> 模块类型 <en/> extension type
 * @param extensions - <zh/> 模块配置项 <en/> extension options
 * @returns <zh/> 标准模块配置项 <en/> Standard extension options
 */
export declare function parseExtensions(graph: Graph, category: string, extensions: TransformOptions): STDExtensionOption[];
