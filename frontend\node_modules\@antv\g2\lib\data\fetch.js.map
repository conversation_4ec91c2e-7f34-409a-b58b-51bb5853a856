{"version": 3, "file": "fetch.js", "sourceRoot": "", "sources": ["../../src/data/fetch.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,gDAAwE;AAGxE,4CAA2C;AAIpC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,EAAE;IACjD,MAAM,EACJ,KAAK,EACL,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAC/B,SAAS,GAAG,GAAG,EACf,QAAQ,GAAG,IAAI,GAChB,GAAG,OAAO,CAAC;IACZ,OAAO,GAAS,EAAE;QAChB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC;QAEpC,IAAI,MAAM,KAAK,KAAK,EAAE;YACpB,+CAA+C;YAC/C,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClC,OAAO,IAAA,kBAAS,EAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,iBAAU,CAAC,CAAC,CAAC,iBAAQ,CAAC,CAAC;SAC1E;QACD,IAAI,MAAM,KAAK,MAAM,EAAE;YACrB,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;SAC9B;QACD,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,GAAG,CAAC,CAAC;IAChD,CAAC,CAAA,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,KAAK,SAoBhB;AAEF,aAAK,CAAC,KAAK,GAAG,EAAE,CAAC"}