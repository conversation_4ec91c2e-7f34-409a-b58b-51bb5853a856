import type { AABB, TextStyleProps } from '@antv/g';
import type { PathArray } from '@antv/util';
import type { CardinalPlacement } from '../types';
/**
 * <zh/> 计算文本位置样式
 *
 * <en/> Calculate text position style
 * @param bounds - <zh/> 外包围盒 | <en/> contour bounds
 * @param placement - <zh/> 位置 | <en/> placement
 * @param offsetX - <zh/> x轴偏移 | <en/> x-axis offset
 * @param offsetY - <zh/> y轴偏移 | <en/> y-axis offset
 * @param closeToContour - <zh/> 标签位置是否贴合轮廓 | <en/> whether the label position is close to the contour
 * @param path - <zh/> 路径 | <en/> path
 * @param autoRotate - <zh/> 是否跟随轮廓旋转 | <en/> whether to rotate with the contour
 * @returns <zh/> 文本样式 | <en/> text style
 */
export declare function getPolygonTextStyleByPlacement(bounds: AABB, placement: CardinalPlacement | 'center', offsetX: number, offsetY: number, closeToContour: boolean, path: PathArray | string, autoRotate: boolean): Partial<TextStyleProps>;
