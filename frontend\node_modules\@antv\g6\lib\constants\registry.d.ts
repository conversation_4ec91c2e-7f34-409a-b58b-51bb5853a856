export declare enum ExtensionCategory {
    /**
     * <zh/> 节点元素
     *
     * <en/> Node element
     */
    NODE = "node",
    /**
     * <zh/> 边元素
     *
     * <en/> Edge element
     */
    EDGE = "edge",
    /**
     * <zh/> 组合元素
     *
     * <en/> Combination element
     */
    COMBO = "combo",
    /**
     * <zh/> 主题
     *
     * <en/> Theme
     */
    THEME = "theme",
    /**
     * <zh/> 色板
     *
     * <en/> Palette
     */
    PALETTE = "palette",
    /**
     * <zh/> 布局
     *
     * <en/> Layout
     */
    LAYOUT = "layout",
    /**
     * <zh/> 交互
     *
     * <en/> Behavior
     */
    BEHAVIOR = "behavior",
    /**
     * <zh/> 插件
     *
     * <en/> Plugin
     */
    PLUGIN = "plugin",
    /**
     * <zh/> 动画
     *
     * <en/> Animation
     */
    ANIMATION = "animation",
    /**
     * <zh/> 数据转换
     *
     * <en/> Data transform
     */
    TRANSFORM = "transform",
    /**
     * <zh/> 图形
     *
     * <en/> Shape
     */
    SHAPE = "shape"
}
