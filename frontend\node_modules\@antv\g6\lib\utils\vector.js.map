{"version": 3, "file": "vector.js", "sourceRoot": "", "sources": ["../../src/utils/vector.ts"], "names": [], "mappings": ";;AAaA,kBAEC;AAUD,4BAEC;AAUD,4BAGC;AAUD,wBAGC;AAUD,kBAEC;AAUD,sBAIC;AAUD,sBAEC;AAUD,4BAEC;AAUD,8CAEC;AASD,8BAGC;AAWD,sBAWC;AAUD,kCAEC;AAUD,sCAEC;AAUD,kBAEC;AASD,8BAEC;AASD,8BAEC;AASD,kBAIC;AA3ND,6BAAiC;AAEjC,MAAM,WAAW,GAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAEvC;;;;;;;GAOG;AACH,SAAgB,GAAG,CAAC,CAAoB,EAAE,CAAoB;IAC5D,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAsB,CAAC;AACxD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,QAAQ,CAAC,CAAoB,EAAE,CAAoB;IACjE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAsB,CAAC;AACxD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,QAAQ,CAAC,CAAoB,EAAE,CAA6B;IAC1E,IAAI,OAAO,CAAC,KAAK,QAAQ;QAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAsB,CAAC;IAC3E,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAsB,CAAC;AACxD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,MAAM,CAAC,CAAoB,EAAE,CAA6B;IACxE,IAAI,OAAO,CAAC,KAAK,QAAQ;QAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAsB,CAAC;IAC3E,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAsB,CAAC;AACxD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,GAAG,CAAC,CAAoB,EAAE,CAAoB;IAC5D,OAAQ,CAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,KAAK,CAAC,CAAoB,EAAE,CAAoB;IAC9D,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IACxB,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IACxB,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACvG,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,KAAK,CAAC,CAAoB,EAAE,CAAS;IACnD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAsB,CAAC;AAClD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,QAAQ,CAAC,CAAoB,EAAE,CAAoB;IACjE,OAAO,IAAI,CAAC,IAAI,CAAE,CAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,SAAA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAI,CAAC,CAAA,EAAE,CAAC,CAAC,CAAC,CAAC;AACzF,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,iBAAiB,CAAC,CAAoB,EAAE,CAAoB;IAC1E,OAAQ,CAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5E,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,SAAS,CAAC,CAAoB;IAC5C,MAAM,MAAM,GAAI,CAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,SAAA,CAAC,EAAI,CAAC,CAAA,EAAE,CAAC,CAAC,CAAC;IACnE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAsB,CAAC;AAClE,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,KAAK,CAAC,CAAoB,EAAE,CAAoB,EAAE,SAAS,GAAG,KAAK;IACjF,MAAM,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAClB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAc,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,CAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QACzE,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CACxD,CAAC;IACF,qEAAqE;IACrE,IAAI,SAAS,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;QACjC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;IAC9B,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,WAAW,CAAC,CAAoB,EAAE,CAAoB;IACpE,OAAQ,CAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,CAAU,EAAE,SAAS,GAAG,IAAI;IACxD,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,GAAG,CAAC,CAAoB,EAAE,CAAS;IACjD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAsB,CAAC;AAClD,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,SAAS,CAAC,CAAoB;IAC5C,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,SAAS,CAAC,CAAoB;IAC5C,OAAO,IAAA,cAAS,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,GAAG,CAAC,CAAoB;IACtC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACjB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAAE,OAAO,CAAC,CAAC;IACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B,CAAC"}