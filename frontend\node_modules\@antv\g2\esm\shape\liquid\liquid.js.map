{"version": 3, "file": "liquid.js", "sourceRoot": "", "sources": ["../../../src/shape/liquid/liquid.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAGxC,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AACjC,OAAO,EAAE,gBAAgB,EAAE,MAAM,UAAU,CAAC;AAE5C,MAAM,cAAc,GAAG,CAAC,KAAK,GAAG,QAAQ,EAAE,EAAE,CAC1C,gBAAgB,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC;AAIrD,MAAM,CAAC,MAAM,MAAM,GAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;IAC5D,IAAI,CAAC,OAAO;QAAE,OAAO;IACrB,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;IAC/B,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;IAChD,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,aAAa,CAAC;IAC/C,MAAM,EACJ,UAAU,EAAE,eAAe,EAC3B,OAAO,GAAG,EAAE,EACZ,IAAI,GAAG,EAAE,KAEP,YAAY,EADX,IAAI,UACL,YAAY,EALV,iCAKL,CAAe,CAAC;IACjB,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,KAAsB,OAAO,EAAxB,YAAY,UAAK,OAAO,EAAvD,sBAA6C,CAAU,CAAC;IAC9D,MAAM,EAAE,MAAM,GAAG,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;IAEzC,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE;QAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;QAC3C,MAAM,KAAK,GAAG,8BAAE,IAAI,EAAE,KAAK,IAAK,WAAW,GAAK,IAAI,CAAoB,CAAC;QAEzE,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAE1C,cAAc;QACd,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;QAClD,kBAAkB;QAClB,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAErC,wCAAwC;QACxC,MAAM,SAAS,GAAG,UAAU,CAAC,WAAW,CAAC;YACvC,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAChC,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QAC/D,MAAM,aAAa,GAAG,SAAS,CAC7B,OAAO,EACP,OAAO,EACP,MAAM,GAAG,MAAM,GAAG,CAAC,EACnB,GAAG,IAAI,CACR,CAAC;QAEF,uBAAuB;QACvB,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE;YACvC,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE;gBACrD,KAAK,kBACH,CAAC,EAAE,SAAS,EACZ,IAAI,EAAE,MAAM,IACT,eAAe,CACnB;aACF,CAAC,CAAC;YAEH,CAAC,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;SAChC;QAED,mCAAmC;QACnC,IAAI,OAAO,GAAG,CAAC,EAAE;YACf,kBAAkB;YAClB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE;gBAC/C,KAAK,EAAE;oBACL,CAAC,EAAE,aAAa;iBACjB;aACF,CAAC,CAAC;YAEH,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACzB,CAAC,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC;YAE7B,kBAAkB;YAClB,OAAO,CACL,OAAO,EACP,OAAO,EACP,CAAC,GAAG,OAAO,EACX,KAAK,EACL,KAAK,EACL,CAAC,EACD,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,EACrB,MAAM,GAAG,CAAC,EACV,MAAM,EACN,IAAI,EACJ,QAAQ,CACT,CAAC;SACH;QAED,oBAAoB;QACpB,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE;YACnD,KAAK,EAAE;gBACL,CAAC,EAAE,SAAS;gBACZ,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,MAAM,GAAG,CAAC,GAAG,QAAQ;gBAChC,MAAM,EAAE,MAAM;aACf;SACF,CAAC,CAAC;QAEH,kBAAkB;QAClB,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE;YACjD,KAAK,8CACH,CAAC,EAAE,SAAS,EACZ,MAAM,EAAE,KAAK,EACb,aAAa,EAAE,WAAW,EAC1B,SAAS,EAAE,MAAM,IACd,KAAK,GACL,YAAY,KACf,IAAI,EAAE,aAAa,GACpB;SACF,CAAC,CAAC;QAEH,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC7B,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAE3B,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC"}