/**
 * <zh/> 比较两个数组的差异
 *
 * <en/> compare the difference between two arrays
 * @param original - <zh/> 原始数组 | <en/> original array
 * @param modified - <zh/> 修改后的数组 | <en/> modified array
 * @param key - <zh/> 比较的 key | <en/> key to compare
 * @param comparator - <zh/> 比较函数 | <en/> compare function
 * @returns <zh/> 数组差异 | <en/> array diff
 */
export declare function arrayDiff<T>(original: T[], modified: T[], key: (d: T) => string | number, comparator?: (a?: T, b?: T) => boolean): {
    enter: T[];
    exit: T[];
    keep: T[];
    update: T[];
};
