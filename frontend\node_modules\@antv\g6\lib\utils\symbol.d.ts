import type { PathArray } from '@antv/util';
export type SymbolFactor = (width: number, height: number) => PathArray;
/**
 * ○
 */
export declare const circle: SymbolFactor;
/**
 * ▷
 */
export declare const triangle: SymbolFactor;
/**
 * ◇
 */
export declare const diamond: SymbolFactor;
/**
 * >>
 */
export declare const vee: SymbolFactor;
/**
 * □
 */
export declare const rect: SymbolFactor;
/**
 * □▷
 */
export declare const triangleRect: SymbolFactor;
/**
 * >
 */
export declare const simple: SymbolFactor;
