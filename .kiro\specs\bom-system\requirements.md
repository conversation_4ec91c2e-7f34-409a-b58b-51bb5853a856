# 需求文档

## 介绍

本文档概述了专为天线行业小微制造企业设计的综合BOM（物料清单）管理系统的需求。该系统旨在建立以"核心BOM（150%）+订单快速派生（100%）+按单合单采购+包装/MOQ取整+长度型切割优化+余料台账+服务BOM"为核心的闭环能力，以减少材料浪费、提升协同效率并增强成本透明度。

系统特别针对小微企业的特点进行设计：
- **人员精简**：一人多岗，需要系统操作简单、学习成本低
- **资金紧张**：需要精确的成本控制和浪费管理
- **订单多样**：小批量、多品种、定制化需求
- **管理粗放**：需要系统自动化处理和智能提醒
- **技术有限**：需要系统稳定可靠、易于维护

## 需求

### 需求1

**用户故事：** 作为BOM管理员，我希望维护一个集中的核心BOM（150%）和配置规则，以便所有订单BOM都能一致地派生，无需手动复制。

#### 验收标准

1. 当创建核心BOM时，系统应支持包含可选/必选/互斥组件的层级结构
2. 当定义配置规则时，系统应验证规则逻辑和依赖关系
3. 当核心BOM版本发布时，系统应冻结版本并防止未授权修改
4. 当需要变更时，系统应要求ECN（工程变更通知）审批流程
5. 当查看BOM差异时，系统应显示版本间的增量对比

### 需求2

**用户故事：** 作为销售/PMC用户，我希望使用客户配置从核心BOM派生订单专用BOM，以便每个订单都有唯一准确的物料清单。

#### 验收标准

1. 当输入客户配置时，系统应自动从核心BOM派生100%的订单BOM
2. 当派生完成时，系统应显示配置增量和物料变更
3. 当生成订单BOM时，系统应将其冻结到特定的核心BOM和规则版本
4. 当需要升级订单BOM时，系统应要求审批流程
5. 当导出订单BOM时，系统应支持多种格式（Excel、PDF、自定义模板）

### 需求3

**用户故事：** 作为采购经理，我希望系统能合并订单并优化包装/MOQ，以便最小化材料浪费并提高采购效率。

#### 验收标准

1. 当运行MRP计算时，系统应在滚动时间窗口内合并需求
2. 当执行包装优化时，系统应向上取整到包装倍数并跟踪预期浪费
3. 当存在MOQ约束时，系统应优化订单数量并将浪费归因到具体原因
4. 当合并完成时，系统应生成带浪费归因的采购建议
5. 当存在紧急订单时，系统应支持白名单绕过关键交付

### 需求4

**用户故事：** 作为生产计划员，我希望对长度型材料进行切割优化并管理余料台账，以便最大化材料利用率并正确跟踪余料。

#### 验收标准

1. 当汇总长度需求时，系统应考虑盘长和切割余量生成最优切割计划
2. 当执行切割计划时，系统应跟踪实际余料并更新余料台账
3. 当新订单需要材料时，系统应优先使用余料再开新盘
4. 当余料低于最小可重用长度时，系统应将其归类为切割浪费
5. 当管理余料库存时，系统应跟踪库位、有效期和预留状态

### 需求5

**用户故事：** 作为财务经理，我希望有全面的成本跟踪和浪费归因，以便标准成本与实际成本透明，并识别浪费来源。

#### 验收标准

1. 当创建订单时，系统应计算包含材料、包装浪费和切割浪费的标准成本
2. 当生产完成时，系统应基于实际消耗和余料回收计算实际成本
3. 当执行成本分析时，系统应按类别（包装、MOQ、切割、过期）分解浪费
4. 当需要毛利分析时，系统应提供实时盈利能力计算
5. 当出现成本差异时，系统应在偏差超过±5%阈值时发出警报

### 需求6

**用户故事：** 作为服务技术员，我希望有服务BOM和带二维码的设备档案，以便快速识别所需备件并访问维护历史。

#### 验收标准

1. 当产品发货时，系统应生成As-Built BOM并创建带二维码的设备档案
2. 当创建服务BOM时，系统应包含备件、互换组和建议库存水平
3. 当扫描二维码时，系统应显示设备详情和可用备件
4. 当创建服务工单时，系统应基于故障代码建议备件并预留库存
5. 当维护完成时，系统应更新As-Maintained记录和保修状态

### 需求7

**用户故事：** 作为质量经理，我希望有ECN工作流和影响分析，以便工程变更得到适当评估并传达到所有受影响区域。

#### 验收标准

1. 当启动ECN时，系统应分析对物料、BOM、订单、库存、成本、供应商和服务BOM的影响
2. 当ECN在审核中时，系统应根据变更范围路由到适当的审批者
3. 当ECN获得批准时，系统应更新受影响的文档并通知利益相关者
4. 当ECN生效时，系统应根据定义的策略（立即、计划或订单特定）应用变更
5. 当发送通知时，系统应跟踪接收确认并维护审计跟踪

### 需求8

**用户故事：** 作为仓库经理，我希望有集成的库存管理和批次跟踪，以便准确记录和追溯物料移动。

#### 验收标准

1. 当收到材料时，系统应记录批次号、盘长和有效期
2. 当发放材料时，系统应遵循切割计划并跟踪实际消耗
3. 当退回余料时，系统应用准确的数量和库位更新余料台账
4. 当需要替代时，系统应验证批准的替代品并记录使用情况
5. 当出现库存差异时，系统应提供差异报告和调整工作流

### 需求9

**用户故事：** 作为系统管理员，我希望有基于角色的访问控制和审计跟踪，以便维护数据安全并记录所有关键操作。

#### 验收标准

1. 当用户访问系统时，系统应基于分配的角色进行身份验证和授权
2. 当显示敏感数据时，系统应对未授权用户屏蔽价格/成本字段
3. 当执行关键操作时，系统应记录用户、时间戳、旧值、新值和原因
4. 当需要审计报告时，系统应提供全面的活动跟踪
5. 当发生安全违规时，系统应警告管理员并阻止未授权访问

### 需求10

**用户故事：** 作为小微企业的多岗位员工，我希望系统操作简单直观，支持快速切换不同业务场景，以便一人能高效处理多种业务。

#### 验收标准

1. 当用户登录时，系统应根据当前业务场景自动显示相关功能模块和待办事项
2. 当处理不同业务时，系统应提供角色快速切换功能，无需重新登录
3. 当执行关键操作时，系统应提供操作向导和智能提示，减少学习成本
4. 当需要批量处理时，系统应支持Excel导入导出和批量操作功能
5. 当移动办公时，系统应支持手机端查看和简单操作（审批、查询、扫码）

### 需求11

**用户故事：** 作为小微企业管理者，我希望系统能自动化处理重复性工作，并提供异常预警，以便减少人工干预和避免业务风险。

#### 验收标准

1. 当订单创建时，系统应自动派生BOM、计算成本、生成采购建议，无需人工干预
2. 当库存不足时，系统应自动发送预警通知到相关人员的微信/钉钉
3. 当价格异常波动时，系统应自动标记并推送给财务人员确认
4. 当交期可能延误时，系统应提前预警并建议应对措施
5. 当系统检测到数据异常时，系统应自动标记并提供修复建议

### 需求12

**用户故事：** 作为业务用户，我希望有实时仪表板和KPI报告，以便监控废料减少、成本绩效和服务效果。

#### 验收标准

1. 当执行浪费分析时，系统应按类别（包装、MOQ、切割、过期）报告浪费率
2. 当审查运营指标时，系统应显示合并率、余料利用率和库存周转率
3. 当分析财务绩效时，系统应显示订单毛利、成本差异和供应商价格趋势
4. 当评估服务绩效时，系统应报告首次修复率、MTTR和备件满足率
5. 当评估数据质量时，系统应识别缺失字段、重复项和不一致性

### 需求13

**用户故事：** 作为小微企业用户，我希望系统具有高度的配置灵活性，能够适应业务变化，无需频繁的系统开发。

#### 验收标准

1. 当业务规则变化时，系统应支持通过配置界面修改编码规则、审批流程、预警阈值
2. 当报表需求变化时，系统应提供可视化报表设计器，支持用户自定义报表
3. 当集成需求变化时，系统应提供标准API接口和数据导入导出模板
4. 当权限需求变化时，系统应支持灵活的权限配置，适应组织架构调整
5. 当业务流程优化时，系统应支持工作流配置，无需代码修改

### 需求14

**用户故事：** 作为现场操作人员，我希望系统支持扫码操作和移动端功能，以便在生产现场快速处理业务。

#### 验收标准

1. 当物料入库时，系统应支持扫码快速录入批次、数量、库位信息
2. 当领料发料时，系统应支持扫码确认物料和数量，自动更新库存
3. 当余料回库时，系统应支持扫码快速入账，自动计算剩余价值
4. 当设备维修时，系统应支持扫码查看设备档案和推荐备件清单
5. 当质量检验时，系统应支持移动端录入检验结果和不合格处理

### 需求15

**用户故事：** 作为小微企业用户，我希望系统能够智能学习和优化，随着使用时间增长提供更精准的建议。

#### 验收标准

1. 当历史数据积累时，系统应基于历史消耗优化切割算法和余料利用策略
2. 当供应商表现数据积累时，系统应智能推荐最优供应商和采购策略
3. 当故障数据积累时，系统应基于故障模式优化备件推荐和库存策略
4. 当成本数据积累时，系统应提供更精准的成本预测和毛利分析
5. 当用户操作习惯形成时，系统应个性化界面布局和快捷操作

### 需求16

**用户故事：** 作为小微企业老板，我希望系统能提供经营决策支持，帮助我掌控业务全局和风险。

#### 验收标准

1. 当查看经营状况时，系统应提供订单毛利趋势、库存周转率、客户回款等关键指标
2. 当评估供应商时，系统应提供供应商价格趋势、交期表现、质量统计等综合评价
3. 当分析产品盈利时，系统应提供产品毛利排行、成本构成分析、浪费归因等详细数据
4. 当预测现金流时，系统应基于订单交期、采购付款、库存占用提供资金需求预测
5. 当识别经营风险时，系统应预警库存积压、客户集中度、供应商依赖等潜在风险

### 需求17

**用户故事：** 作为小微企业的兼职会计，我希望系统能自动生成财务数据，减少手工核算工作。

#### 验收标准

1. 当物料入库时，系统应自动生成入库成本和应付账款数据
2. 当生产领料时，系统应自动计算生产成本和在制品价值
3. 当产品完工时，系统应自动结转制造成本到产成品
4. 当销售出库时，系统应自动计算销售成本和毛利
5. 当月末结账时，系统应提供库存明细、成本分析、损益统计等财务报表

### 需求18

**用户故事：** 作为客户服务人员，我希望系统能快速响应客户询价和售后需求，提升客户满意度。

#### 验收标准

1. 当客户询价时，系统应根据配置快速计算报价，包含材料成本、加工费、毛利
2. 当客户要求交期时，系统应基于库存、采购周期、生产能力给出准确交期承诺
3. 当客户报修时，系统应通过扫码快速定位设备信息和推荐解决方案
4. 当客户需要备件时，系统应快速查询兼容备件和价格，支持在线下单
5. 当处理客户投诉时，系统应提供完整的质量追溯信息和处理记录

### 需求19

**用户故事：** 作为小微企业的技术人员，我希望系统支持产品快速迭代和工程变更，适应市场需求变化。

#### 验收标准

1. 当产品升级时，系统应支持快速复制现有BOM并进行差异化修改
2. 当客户定制时，系统应支持在标准BOM基础上快速配置个性化方案
3. 当供应商停产时，系统应快速识别影响范围并推荐替代方案
4. 当成本压力增大时，系统应分析成本构成并推荐降成本措施
5. 当质量问题出现时，系统应快速定位问题批次并评估影响范围

### 需求20

**用户故事：** 作为小微企业用户，我希望系统具备良好的容错性和数据安全性，避免因操作失误造成损失。

#### 验收标准

1. 当用户误操作时，系统应提供操作撤销和数据恢复功能
2. 当关键数据修改时，系统应要求二次确认并记录修改原因
3. 当系统故障时，系统应自动备份数据并提供快速恢复机制
4. 当网络中断时，系统应支持离线操作和数据同步功能
5. 当数据异常时，系统应自动检测并提供数据修复建议