{"version": 3, "file": "fetch.js", "sourceRoot": "", "sources": ["../../src/data/fetch.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,QAAQ,IAAI,UAAU,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAGxE,OAAO,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAI3C,MAAM,CAAC,MAAM,KAAK,GAAqB,CAAC,OAAO,EAAE,EAAE;IACjD,MAAM,EACJ,KAAK,EACL,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAC/B,SAAS,GAAG,GAAG,EACf,QAAQ,GAAG,IAAI,GAChB,GAAG,OAAO,CAAC;IACZ,OAAO,GAAS,EAAE;QAChB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC;QAEpC,IAAI,MAAM,KAAK,KAAK,EAAE;YACpB,+CAA+C;YAC/C,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClC,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;SAC1E;QACD,IAAI,MAAM,KAAK,MAAM,EAAE;YACrB,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;SAC9B;QACD,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,GAAG,CAAC,CAAC;IAChD,CAAC,CAAA,CAAC;AACJ,CAAC,CAAC;AAEF,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC"}