import type { <PERSON>, Graph as <PERSON><PERSON><PERSON><PERSON>, Node } from '@antv/graphlib';
import type { EdgeData } from '../spec';
import { NodeLikeData } from '../types/data';
export declare function toGraphlibData(datums: EdgeData): Edge<EdgeData>;
export declare function toGraphlibData(datums: NodeLikeData): Node<NodeLikeData>;
export declare function toG6Data<T extends EdgeData>(data: Edge<T>): T;
export declare function toG6Data<T extends NodeLikeData>(data: Node<T>): T;
/**
 * <zh/> 创建树形结构
 *
 * <en/> Create tree structure
 * @param model - <zh/> 数据模型 | <en/> data model
 */
export declare function createTreeStructure(model: Graphlib<any, any>): void;
